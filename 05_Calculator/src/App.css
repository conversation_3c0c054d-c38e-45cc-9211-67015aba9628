body {
  margin: 0;
  font-family: "Segoe UI", sans-serif;
  background: linear-gradient(135deg, #0f0f0f, #1a1a1a);
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.calculator {
  width: 320px;
  padding: 20px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5);
}

.display {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  padding: 15px;
  font-size: 28px;
  text-align: right;
  border-radius: 10px;
  margin-bottom: 20px;
  overflow-x: auto;
}

.buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

button {
  height: 60px;
  font-size: 18px;
  color: white;
  background: rgba(255, 255, 255, 0.08);
  border: none;
  border-radius: 10px;
  cursor: pointer;
  backdrop-filter: blur(6px);
  transition: background 0.3s, transform 0.2s;
}

button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

button.clear {
  grid-column: span 4;
  background: rgba(255, 0, 0, 0.4);
  font-weight: bold;
}
