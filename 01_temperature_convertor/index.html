<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Temperature Converter</title>
  <link rel="stylesheet" href="style.css" />
</head>
<body>
  <div class="converter">
    <h2> Temperature Converter</h2>
    <input type="number" id="temperatureInput" placeholder="Enter temperature" />
    <select id="conversionType">
      <option value="CtoF">Celsius → Fahrenheit</option>
      <option value="FtoC">Fahrenheit → Celsius</option>
    </select>
    <br />
    <button onclick="convertTemperature()">Convert</button>
    <div class="result" id="result"></div>
  </div>

  <script src="script.js"></script>
</body>
</html>
